import configparser
import os
import win32com.client
from task.base_activity import <PERSON>celHandler
from task.base_activity import <PERSON><PERSON>ogger
from task.base_activity import DirectoryReader
from task.base_activity import PSFileHandler


class Psclass:
    def __init__(self):
        self.keyword = "替换内容"
        self.config_file = "auto_replace.ini"
        self.excel_file = ""
        self.sheet_name = ""
        self.tif_file = ""
        self.logger = BaseLogger()

    def read_config(self):
        config = configparser.ConfigParser()
        config.read(self.config_file, encoding='utf-8')
        if not config.has_section("ExcelFile"):
            raise ValueError("配置文件中缺少 'ExcelFile' 部分")
        if not config.has_section("Normalize"):
            raise ValueError("配置文件中缺少 'Normalize' 部分")
        if not config.has_section("PsConfig"):
            raise ValueError("配置文件中缺少 'PsConfig' 部分")

        if not config.has_option("ExcelFile", "sheet_name"):
            raise ValueError("配置文件中缺少 'sheet_name' 选项")
        self.sheet_name = config.get("ExcelFile", "sheet_name")

        if not config.has_option("ExcelFile", "excel_file"):
            raise ValueError("配置文件中缺少 'excel_file' 选项")
        self.excel_file = config.get("ExcelFile", "excel_file")

        if not config.has_option("Normalize", "keyword"):
            raise ValueError("配置文件中缺少 'keyword' 选项")
        self.keyword = config.get("Normalize", "keyword")
        if not config.has_option("PsConfig", "tif_file"):
            raise ValueError("配置文件中缺少 'tif_file' 选项")
        self.tif_file = config.get("PsConfig", "tif_file")
        self.logger.log_info(f"读取配置文件成功: {self.excel_file}, {self.sheet_name}, {self.tif_file}, {self.keyword}")
    
    def run(self):
        self.ps_file_handle = PSFileHandler()
        self.excel_handler = ExcelHandler(self.excel_file)

        excel_obj = self.excel_handler.open_excel()
        psd_data = self.excel_handler.read_sheet_data(excel_obj, self.sheet_name)
        if not os.path.exists(self.tif_file):
            self.logger.log_error(f"输入的tif文件路径不存在: {self.tif_file}")
            return
        if not os.path.exists(self.excel_file):
            self.logger.log_error(f"excel文件路径不存在: {self.excel_file}")
            return

        for index, row in enumerate(psd_data):
            if index == 0:
                if "运行结果" in [cell.value for cell in excel_obj.Sheets(self.sheet_name).Rows(1)]:
                    # 获取 "运行结果" 列的索引
                    result_col_index = [cell.value for cell in excel_obj.Sheets(
                        self.sheet_name).Rows(1)].index("运行结果") + 1
                    # 清空该列内容，保留第一行表头
                    for row_idx in range(2, excel_obj.Sheets(self.sheet_name).UsedRange.Rows.Count + 1):
                        self.excel_handler.write_to_cell(
                            excel_obj, self.sheet_name, row_idx, result_col_index, "")
                else:
                    self.excel_handler.write_to_cell(
                        excel_obj, self.sheet_name, 1, len(row) + 1, f"运行结果")
                    result_col_index = len(row) + 1
                continue
            current_num = index + 1
            psd_file = os.path.join(row[3], row[1])  # 假设文件名在第一列
            if not os.path.exists(psd_file):
                self.logger.log_error(f"psd文件路径不存在: {psd_file}")
                self.excel_handler.write_to_cell(
                    excel_obj, self.sheet_name, current_num, result_col_index, f"失败-psd文件路径不存在: {psd_file}")
                continue

            export_dir = f"{row[4]}\{row[2]}"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            self.logger.log_info(f"正在处理文件: {psd_file}")
            self.ps_file_handle.open_ps_file(psd_file)

            app = win32com.client.Dispatch("Photoshop.Application")
            # app.Visible = True

            def get_layer_names(layer_set, names):
                # 遍历普通图层
                for i in range(1, layer_set.ArtLayers.Count + 1):
                    layer = layer_set.ArtLayers.Item(i)
                    names.append((layer.Name, layer.Kind))
                # 遍历组（LayerSet），递归
                for i in range(1, layer_set.LayerSets.Count + 1):
                    group = layer_set.LayerSets.Item(i)
                    names.append((group.Name, "LayerSet"))
                    get_layer_names(group, names)

            layer_infos = []
            doc = app.ActiveDocument  # 获取当前打开的文档
            get_layer_names(doc, layer_infos)
            replace_layer_names = []
            for name, kind in layer_infos:
                if self.keyword in name:
                    print(f"图层名称: {name}, 类型: {kind}")
                    # 这里可以添加更多的处理逻辑，比如替换图层名称等
                    replace_layer_names.append(name)
            replace_layer_names = list(set(replace_layer_names))
            for name in replace_layer_names:

                self.ps_file_handle.replace_img(name, self.tif_file)
            
            self.ps_file_handle.export_psd_to_jpg_with_quality_all_group(app=app,export_dir=export_dir,remark=row[2])

            self.excel_handler.write_to_cell(
                excel_obj, self.sheet_name, current_num, result_col_index, f"成功"
            )
            self.ps_file_handle.close_ps_file()
if __name__ == "__main__":
    task = Psclass()
    task.read_config()
    task.run()