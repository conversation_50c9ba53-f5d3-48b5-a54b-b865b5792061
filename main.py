import activity
import activity.彩底_挂绳单个装
import activity.侧边瞳眼_挂绳
import activity.彩底磨砂挂绳彩绘Aadd
import task
import os
#配置索引目录

folderIds = {
    "彩底磨砂挂绳彩绘A+": activity.彩底磨砂挂绳彩绘Aadd,
    "侧边瞳眼_挂绳": activity.侧边瞳眼_挂绳,
    "彩底_挂绳单个装": activity.彩底_挂绳单个装,
}

folder_path = r"\\192.168.1.188\图片集散\4月-自动化需求"

for subfolder in os.listdir(folder_path):
    subfolder_path = os.path.join(folder_path, subfolder)
    if os.path.isdir(subfolder_path):
        # print(f"Subfolder: {subfolder_path}")
        # Check if the subfolder name contains "任务模版" and "任务状态"
        if "任务模版" in subfolder and "任务状态" in subfolder:
            # Extract the task template and task status
            parts = subfolder.split("-")
            # try:
            task_template_index = parts.index("任务模版") + 1
            task_status_index = parts.index("任务状态") + 1
            task_template = parts[task_template_index]
            task_status = parts[task_status_index]
            print(f"任务模版: {task_template},任务状态: {task_status}")
            Realy_task_template = folderIds.get(task_template, None)
            if Realy_task_template != None and task_status != None:
                if task_status == "已完成":
                    print("任务状态完成，跳过")
                    continue
                
                # Scan for .xlsx files in the subfolder
                xlsx_files = [f for f in os.listdir(subfolder_path) if f.endswith('.xlsx') and "~$" not in f]
                if not xlsx_files:
                    print(f"No .xlsx files found in {subfolder_path}.")
                    print("不存在模版文件，跳过")
                    continue
                else:
                    print(f"Found .xlsx files: {xlsx_files}")
                temp_task_file = os.path.join(subfolder_path, xlsx_files[0])
                print("任务模版:",temp_task_file)
                Realy_task_template.run(subfolder_path,temp_task_file)
                # Update the task status to "已完成"
                new_subfolder_name = subfolder.replace("任务状态-待处理", "任务状态-已完成")
                new_subfolder_path = os.path.join(folder_path, new_subfolder_name)
                os.rename(subfolder_path, new_subfolder_path)
                print(f"任务状态已更新为已完成: {new_subfolder_path}")

            # except (ValueError, IndexError):






            #     print("Error extracting task template or task status.")
